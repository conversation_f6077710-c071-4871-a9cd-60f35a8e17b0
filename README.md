# PHP CRUD Application with MVC Architecture

A modern PHP CRUD (Create, Read, Update, Delete) application built with MVC architecture, Composer autoloading, and role-based authentication.

## 🚀 New MVC Version Available!

This application now includes a **modern MVC implementation** alongside the original procedural code:

- **MVC Architecture**: Clean separation of concerns with Models, Views, and Controllers
- **Composer Integration**: PSR-4 autoloading and dependency management
- **Clean URLs**: SEO-friendly routing system
- **Modern PHP Practices**: Namespaces, autoloading, and object-oriented design

### Quick Start with MVC Version
1. Run `composer install` to install dependencies
2. Access the MVC version at: `http://localhost/php_crud/public/`
3. The original version is still available at: `http://localhost/php_crud/`

## Features

- **User Authentication**: Login/logout functionality with session management
- **Role-based Access Control**: 
  - **Admin**: Can view, create, edit, and delete all records
  - **User**: Can only view, create, edit, and delete their own records
- **Secure Password Handling**: Passwords are hashed using P<PERSON>'s `password_hash()` function
- **SQL Injection Prevention**: Uses prepared statements for database queries
- **Responsive Design**: Bootstrap-based UI that works on desktop and mobile

## Setup Instructions

### 1. Database Setup

1. Create a MySQL database named `php_crud`
2. Run the SQL script in `database_setup.sql` to create the required tables and insert demo users

```sql
-- You can run this in phpMyAdmin or MySQL command line
source database_setup.sql;
```

### 2. Configuration

The database connection settings are in `connection.php`. Update if needed:

```php
$server = "localhost";
$user = "root";
$pass = "";
$db = "php_crud";
```

### 3. Demo Accounts

The setup script creates two demo accounts:

- **Admin Account**: 
  - Username: `admin`
  - Password: `admin123`
  - Can see and manage all records

- **User Account**: 
  - Username: `testuser`
  - Password: `user123`
  - Can only see and manage their own records

### 4. File Structure

```
php_crud/
├── auth.php              # Authentication helper functions
├── connection.php        # Database connection
├── database_setup.sql    # Database setup script
├── index.php            # Main dashboard (shows records)
├── login.php            # Login page
├── logout.php           # Logout handler
├── register.php         # User registration page
├── create.php           # Create new record
├── edit.php             # Edit existing record
├── delete.php           # Delete record
└── README.md            # This file
```

## Usage

1. **Access the Application**: Navigate to `http://localhost/php_crud/`
2. **Login**: You'll be redirected to the login page if not authenticated
3. **Dashboard**: After login, you'll see the main dashboard with records
4. **Create Records**: Click "Add New Client" to create new records
5. **Edit/Delete**: Use the action buttons in the table to edit or delete records

## Security Features

- **Session Management**: Secure session handling for user authentication
- **Password Hashing**: All passwords are hashed using `password_hash()`
- **Prepared Statements**: All database queries use prepared statements to prevent SQL injection
- **Access Control**: Users can only access their own records (except admins)
- **Input Validation**: Form inputs are validated and sanitized

## Database Schema

### Users Table
```sql
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('user','admin') NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### Clients Table
```sql
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
);
```

## Troubleshooting

1. **Database Connection Issues**: Check your database credentials in `connection.php`
2. **Permission Errors**: Ensure your web server has read/write permissions to the PHP files
3. **Session Issues**: Make sure PHP sessions are enabled on your server
4. **Password Issues**: If you can't login, check that the demo passwords haven't been changed

## Extending the Application

- Add more user roles (e.g., manager, viewer)
- Implement email verification for registration
- Add password reset functionality
- Implement audit logging for record changes
- Add data export/import features
