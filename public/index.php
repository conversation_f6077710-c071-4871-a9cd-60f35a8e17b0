<?php

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

use App\Core\Router;

// Create router instance
$router = new Router();

// Authentication routes
$router->get('/auth/login', 'AuthController@showLogin');
$router->post('/auth/login', 'AuthController@login');
$router->get('/auth/register', 'AuthController@showRegister');
$router->post('/auth/register', 'AuthController@register');
$router->get('/auth/logout', 'AuthController@logout');

// Home/Dashboard routes
$router->get('/', 'HomeController@index');
$router->get('/dashboard', 'HomeController@index');

// Client routes
$router->get('/clients/create', 'ClientController@create');
$router->post('/clients', 'ClientController@store');
$router->get('/clients/{id}/edit', 'ClientController@edit');
$router->post('/clients/{id}', 'ClientController@update');
$router->get('/clients/{id}/delete', 'ClientController@delete');

// Handle method override for PUT/DELETE requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['_method'])) {
    $_SERVER['REQUEST_METHOD'] = strtoupper($_POST['_method']);
}

// Dispatch the request
try {
    $router->dispatch();
} catch (Exception $e) {
    // Handle errors
    http_response_code(500);
    echo "Error: " . $e->getMessage();
}
