<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'App\\Controllers\\AuthController' => $baseDir . '/app/Controllers/AuthController.php',
    'App\\Controllers\\ClientController' => $baseDir . '/app/Controllers/ClientController.php',
    'App\\Controllers\\HomeController' => $baseDir . '/app/Controllers/HomeController.php',
    'App\\Core\\Auth' => $baseDir . '/app/Core/Auth.php',
    'App\\Core\\Controller' => $baseDir . '/app/Core/Controller.php',
    'App\\Core\\Database' => $baseDir . '/app/Core/Database.php',
    'App\\Core\\Model' => $baseDir . '/app/Core/Model.php',
    'App\\Core\\Router' => $baseDir . '/app/Core/Router.php',
    'App\\Models\\Client' => $baseDir . '/app/Models/Client.php',
    'App\\Models\\User' => $baseDir . '/app/Models/User.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
);
