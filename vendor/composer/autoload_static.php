<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita9e1db879a4e38ddad033a7837a352f4
{
    public static $prefixLengthsPsr4 = array (
        'T' => 
        array (
            'Tests\\' => 6,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Tests\\' => 
        array (
            0 => __DIR__ . '/../..' . '/tests',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'App\\Controllers\\AuthController' => __DIR__ . '/../..' . '/app/Controllers/AuthController.php',
        'App\\Controllers\\ClientController' => __DIR__ . '/../..' . '/app/Controllers/ClientController.php',
        'App\\Controllers\\HomeController' => __DIR__ . '/../..' . '/app/Controllers/HomeController.php',
        'App\\Core\\Auth' => __DIR__ . '/../..' . '/app/Core/Auth.php',
        'App\\Core\\Controller' => __DIR__ . '/../..' . '/app/Core/Controller.php',
        'App\\Core\\Database' => __DIR__ . '/../..' . '/app/Core/Database.php',
        'App\\Core\\Model' => __DIR__ . '/../..' . '/app/Core/Model.php',
        'App\\Core\\Router' => __DIR__ . '/../..' . '/app/Core/Router.php',
        'App\\Models\\Client' => __DIR__ . '/../..' . '/app/Models/Client.php',
        'App\\Models\\User' => __DIR__ . '/../..' . '/app/Models/User.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita9e1db879a4e38ddad033a7837a352f4::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita9e1db879a4e38ddad033a7837a352f4::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita9e1db879a4e38ddad033a7837a352f4::$classMap;

        }, null, ClassLoader::class);
    }
}
