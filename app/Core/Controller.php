<?php

namespace App\Core;

abstract class Controller
{
    protected function view($view, $data = [])
    {
        // Extract data array to variables
        extract($data);
        
        // Build the view path
        $viewPath = __DIR__ . '/../Views/' . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewPath)) {
            require_once $viewPath;
        } else {
            throw new \Exception("View not found: {$view}");
        }
    }

    protected function redirect($url)
    {
        header("Location: {$url}");
        exit();
    }

    protected function json($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }

    protected function input($key, $default = null)
    {
        if (isset($_POST[$key])) {
            return $_POST[$key];
        }
        
        if (isset($_GET[$key])) {
            return $_GET[$key];
        }
        
        return $default;
    }

    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            
            if (strpos($rule, 'required') !== false && empty($value)) {
                $errors[$field] = ucfirst($field) . ' is required';
                continue;
            }
            
            if (strpos($rule, 'email') !== false && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = ucfirst($field) . ' must be a valid email';
            }
            
            if (preg_match('/min:(\d+)/', $rule, $matches) && !empty($value) && strlen($value) < $matches[1]) {
                $errors[$field] = ucfirst($field) . ' must be at least ' . $matches[1] . ' characters';
            }
            
            if (preg_match('/max:(\d+)/', $rule, $matches) && !empty($value) && strlen($value) > $matches[1]) {
                $errors[$field] = ucfirst($field) . ' must not exceed ' . $matches[1] . ' characters';
            }
        }
        
        return $errors;
    }

    protected function session($key, $value = null)
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if ($value !== null) {
            $_SESSION[$key] = $value;
            return $value;
        }
        
        return $_SESSION[$key] ?? null;
    }

    protected function flash($key, $message = null)
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if ($message !== null) {
            $_SESSION['flash'][$key] = $message;
            return;
        }
        
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        return $message;
    }
}
