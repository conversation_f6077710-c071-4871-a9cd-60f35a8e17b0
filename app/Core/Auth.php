<?php

namespace App\Core;

use App\Models\User;

class Auth
{
    private static $instance = null;
    private $user = null;

    private function __construct()
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $this->loadUser();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadUser()
    {
        if (isset($_SESSION['user_id'])) {
            $userModel = new User();
            $this->user = $userModel->find($_SESSION['user_id']);
        }
    }

    public function attempt($username, $password)
    {
        $userModel = new User();
        $user = $userModel->findByUsernameOrEmail($username);
        
        if ($user && password_verify($password, $user['password'])) {
            $this->login($user);
            return true;
        }
        
        return false;
    }

    public function login($user)
    {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        
        $this->user = $user;
    }

    public function logout()
    {
        session_unset();
        session_destroy();
        $this->user = null;
    }

    public function check()
    {
        return $this->user !== null;
    }

    public function guest()
    {
        return $this->user === null;
    }

    public function user()
    {
        return $this->user;
    }

    public function id()
    {
        return $this->user ? $this->user['id'] : null;
    }

    public function isAdmin()
    {
        return $this->user && $this->user['role'] === 'admin';
    }

    public function requireAuth()
    {
        if ($this->guest()) {
            header('Location: /php_crud/public/auth/login');
            exit();
        }
    }

    public function requireAdmin()
    {
        $this->requireAuth();
        
        if (!$this->isAdmin()) {
            header('Location: /php_crud/public/?error=access_denied');
            exit();
        }
    }

    public function canAccessRecord($recordId)
    {
        if ($this->isAdmin()) {
            return true;
        }
        
        if ($this->check()) {
            // Check if user owns the record
            $db = Database::getInstance();
            $result = $db->query("SELECT created_by FROM clients WHERE id = ?", [$recordId]);
            
            if ($result && $result->num_rows > 0) {
                $record = $result->fetch_assoc();
                return $record['created_by'] == $this->id();
            }
        }
        
        return false;
    }

    // Prevent cloning
    public function __clone()
    {
        throw new \Exception("Cannot clone singleton");
    }

    // Prevent unserialization
    public function __wakeup()
    {
        throw new \Exception("Cannot unserialize singleton");
    }
}
