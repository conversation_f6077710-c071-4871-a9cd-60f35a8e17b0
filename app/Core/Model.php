<?php

namespace App\Core;

abstract class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $result = $this->db->query($sql, [$id]);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }

    public function all($orderBy = null, $direction = 'ASC')
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy} {$direction}";
        }
        
        $result = $this->db->query($sql);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }

    public function where($column, $operator, $value)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} {$operator} ?";
        $result = $this->db->query($sql, [$value]);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }

    public function create($data)
    {
        $filteredData = $this->filterFillable($data);
        
        if (empty($filteredData)) {
            return false;
        }

        $columns = implode(', ', array_keys($filteredData));
        $placeholders = str_repeat('?,', count($filteredData) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return false;
        }

        $types = str_repeat('s', count($filteredData));
        $stmt->bind_param($types, ...array_values($filteredData));
        
        $result = $stmt->execute();
        
        if ($result) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }

    public function update($id, $data)
    {
        $filteredData = $this->filterFillable($data);
        
        if (empty($filteredData)) {
            return false;
        }

        $setParts = [];
        foreach (array_keys($filteredData) as $column) {
            $setParts[] = "{$column} = ?";
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts) . " WHERE {$this->primaryKey} = ?";
        
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return false;
        }

        $values = array_values($filteredData);
        $values[] = $id;
        
        $types = str_repeat('s', count($values));
        $stmt->bind_param($types, ...$values);
        
        return $stmt->execute();
    }

    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return false;
        }

        $stmt->bind_param('i', $id);
        return $stmt->execute();
    }

    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    public function query($sql, $params = [])
    {
        return $this->db->query($sql, $params);
    }
}
