<?php

namespace App\Models;

use App\Core\Model;

class Client extends Model
{
    protected $table = 'clients';
    protected $fillable = ['name', 'email', 'phone', 'address', 'created_by'];

    public function getAllWithCreator()
    {
        $sql = "SELECT c.*, u.username as creator_name
                FROM {$this->table} c
                LEFT JOIN users u ON c.created_by = u.id
                ORDER BY c.created_at DESC";
        
        $result = $this->db->query($sql);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }

    public function getByUserId($userId)
    {
        $sql = "SELECT c.*, u.username as creator_name
                FROM {$this->table} c
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.created_by = ?
                ORDER BY c.created_at DESC";
        
        $result = $this->db->query($sql, [$userId]);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }

    public function findWithCreator($id)
    {
        $sql = "SELECT c.*, u.username as creator_name
                FROM {$this->table} c
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.id = ?";
        
        $result = $this->db->query($sql, [$id]);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }

    public function createClient($data, $userId)
    {
        $data['created_by'] = $userId;
        return $this->create($data);
    }

    public function search($query, $userId = null)
    {
        $sql = "SELECT c.*, u.username as creator_name
                FROM {$this->table} c
                LEFT JOIN users u ON c.created_by = u.id
                WHERE (c.name LIKE ? OR c.email LIKE ? OR c.phone LIKE ?)";
        
        $params = ["%{$query}%", "%{$query}%", "%{$query}%"];
        
        if ($userId !== null) {
            $sql .= " AND c.created_by = ?";
            $params[] = $userId;
        }
        
        $sql .= " ORDER BY c.created_at DESC";
        
        $result = $this->db->query($sql, $params);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }

    public function getStats($userId = null)
    {
        $sql = "SELECT 
                    COUNT(*) as total_clients,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_clients
                FROM {$this->table}";
        
        $params = [];
        
        if ($userId !== null) {
            $sql .= " WHERE created_by = ?";
            $params[] = $userId;
        }
        
        $result = $this->db->query($sql, $params);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return ['total_clients' => 0, 'recent_clients' => 0];
    }
}
