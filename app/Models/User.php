<?php

namespace App\Models;

use App\Core\Model;

class User extends Model
{
    protected $table = 'users';
    protected $fillable = ['username', 'email', 'password', 'role'];

    public function findByUsernameOrEmail($identifier)
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ? OR email = ?";
        $result = $this->db->query($sql, [$identifier, $identifier]);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }

    public function existsByUsernameOrEmail($username, $email)
    {
        $sql = "SELECT id FROM {$this->table} WHERE username = ? OR email = ?";
        $result = $this->db->query($sql, [$username, $email]);
        
        return $result && $result->num_rows > 0;
    }

    public function createUser($data)
    {
        // Hash password before saving
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        // Set default role if not provided
        if (!isset($data['role'])) {
            $data['role'] = 'user';
        }
        
        return $this->create($data);
    }

    public function updatePassword($id, $newPassword)
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($id, ['password' => $hashedPassword]);
    }

    public function getAllWithStats()
    {
        $sql = "SELECT u.*, 
                       COUNT(c.id) as client_count,
                       MAX(c.created_at) as last_client_created
                FROM {$this->table} u
                LEFT JOIN clients c ON u.id = c.created_by
                GROUP BY u.id
                ORDER BY u.created_at DESC";
        
        $result = $this->db->query($sql);
        
        if ($result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }
        
        return [];
    }
}
