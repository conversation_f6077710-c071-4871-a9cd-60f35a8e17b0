<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Auth;
use App\Models\Client;

class HomeController extends Controller
{
    private $auth;

    public function __construct()
    {
        $this->auth = Auth::getInstance();
    }

    public function index()
    {
        $this->auth->requireAuth();

        $clientModel = new Client();
        
        if ($this->auth->isAdmin()) {
            $clients = $clientModel->getAllWithCreator();
        } else {
            $clients = $clientModel->getByUserId($this->auth->id());
        }

        $stats = $clientModel->getStats($this->auth->isAdmin() ? null : $this->auth->id());

        $this->view('clients.index', [
            'clients' => $clients,
            'stats' => $stats,
            'user' => $this->auth->user(),
            'isAdmin' => $this->auth->isAdmin(),
            'error' => $this->input('error'),
            'success' => $this->flash('success')
        ]);
    }
}
