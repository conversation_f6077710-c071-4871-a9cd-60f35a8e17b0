<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Auth;
use App\Models\User;

class AuthController extends Controller
{
    private $auth;

    public function __construct()
    {
        $this->auth = Auth::getInstance();
    }

    public function showLogin()
    {
        if ($this->auth->check()) {
            $this->redirect('/php_crud/public/');
        }

        $this->view('auth.login', [
            'error' => $this->flash('error'),
            'success' => $this->flash('success')
        ]);
    }

    public function login()
    {
        $username = $this->input('username');
        $password = $this->input('password');

        $errors = $this->validate([
            'username' => $username,
            'password' => $password
        ], [
            'username' => 'required',
            'password' => 'required'
        ]);

        if (!empty($errors)) {
            $this->flash('error', 'Please fill in all fields.');
            $this->redirect('/php_crud/public/auth/login');
        }

        if ($this->auth->attempt($username, $password)) {
            $this->redirect('/php_crud/public/');
        } else {
            $this->flash('error', 'Invalid username or password.');
            $this->redirect('/php_crud/public/auth/login');
        }
    }

    public function showRegister()
    {
        if ($this->auth->check()) {
            $this->redirect('/php_crud/public/');
        }

        $this->view('auth.register', [
            'error' => $this->flash('error'),
            'success' => $this->flash('success')
        ]);
    }

    public function register()
    {
        $username = $this->input('username');
        $email = $this->input('email');
        $password = $this->input('password');
        $confirmPassword = $this->input('confirm_password');
        $role = $this->input('role', 'user');

        $errors = $this->validate([
            'username' => $username,
            'email' => $email,
            'password' => $password
        ], [
            'username' => 'required|min:3|max:50',
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);

        if (!empty($errors)) {
            $this->flash('error', implode('<br>', $errors));
            $this->redirect('/php_crud/public/auth/register');
        }

        if ($password !== $confirmPassword) {
            $this->flash('error', 'Passwords do not match.');
            $this->redirect('/php_crud/public/auth/register');
        }

        $userModel = new User();
        
        if ($userModel->existsByUsernameOrEmail($username, $email)) {
            $this->flash('error', 'Username or email already exists.');
            $this->redirect('/php_crud/public/auth/register');
        }

        $userId = $userModel->createUser([
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'role' => $role
        ]);

        if ($userId) {
            $this->flash('success', 'Registration successful! Please login.');
            $this->redirect('/php_crud/public/auth/login');
        } else {
            $this->flash('error', 'Registration failed. Please try again.');
            $this->redirect('/php_crud/public/auth/register');
        }
    }

    public function logout()
    {
        $this->auth->logout();
        $this->redirect('/php_crud/public/auth/login');
    }
}
