<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Auth;
use App\Models\Client;

class ClientController extends Controller
{
    private $auth;
    private $clientModel;

    public function __construct()
    {
        $this->auth = Auth::getInstance();
        $this->clientModel = new Client();
    }

    public function create()
    {
        $this->auth->requireAuth();

        $this->view('clients.create', [
            'error' => $this->flash('error'),
            'success' => $this->flash('success'),
            'user' => $this->auth->user()
        ]);
    }

    public function store()
    {
        $this->auth->requireAuth();

        $name = $this->input('name');
        $email = $this->input('email');
        $phone = $this->input('phone');
        $address = $this->input('address');

        $errors = $this->validate([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'address' => $address
        ], [
            'name' => 'required|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'required|max:20',
            'address' => 'required|max:255'
        ]);

        if (!empty($errors)) {
            $this->flash('error', implode('<br>', $errors));
            $this->redirect('/php_crud/public/clients/create');
        }

        $clientId = $this->clientModel->createClient([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'address' => $address
        ], $this->auth->id());

        if ($clientId) {
            $this->flash('success', 'Client created successfully!');
            $this->redirect('/php_crud/public/');
        } else {
            $this->flash('error', 'Failed to create client. Please try again.');
            $this->redirect('/php_crud/public/clients/create');
        }
    }

    public function edit($id)
    {
        $this->auth->requireAuth();

        if (!$this->auth->canAccessRecord($id)) {
            $this->redirect('/php_crud/public/?error=access_denied');
        }

        $client = $this->clientModel->findWithCreator($id);

        if (!$client) {
            $this->flash('error', 'Client not found.');
            $this->redirect('/php_crud/public/');
        }

        $this->view('clients.edit', [
            'client' => $client,
            'error' => $this->flash('error'),
            'success' => $this->flash('success'),
            'user' => $this->auth->user()
        ]);
    }

    public function update($id)
    {
        $this->auth->requireAuth();

        if (!$this->auth->canAccessRecord($id)) {
            $this->redirect('/php_crud/public/?error=access_denied');
        }

        $name = $this->input('name');
        $email = $this->input('email');
        $phone = $this->input('phone');
        $address = $this->input('address');

        $errors = $this->validate([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'address' => $address
        ], [
            'name' => 'required|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'required|max:20',
            'address' => 'required|max:255'
        ]);

        if (!empty($errors)) {
            $this->flash('error', implode('<br>', $errors));
            $this->redirect("/php_crud/public/clients/{$id}/edit");
        }

        $success = $this->clientModel->update($id, [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'address' => $address
        ]);

        if ($success) {
            $this->flash('success', 'Client updated successfully!');
            $this->redirect('/php_crud/public/');
        } else {
            $this->flash('error', 'Failed to update client. Please try again.');
            $this->redirect("/php_crud/public/clients/{$id}/edit");
        }
    }

    public function delete($id)
    {
        $this->auth->requireAuth();

        if (!$this->auth->canAccessRecord($id)) {
            $this->redirect('/php_crud/public/?error=access_denied');
        }

        $success = $this->clientModel->delete($id);

        if ($success) {
            $this->flash('success', 'Client deleted successfully!');
        } else {
            $this->flash('error', 'Failed to delete client.');
        }

        $this->redirect('/php_crud/public/');
    }
}
