<?php
$title = 'Dashboard - PHP CRUD';
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><?= $isAdmin ? 'All Client Records' : 'My Client Records' ?></h2>
        <p class="text-muted mb-0">
            Total: <?= $stats['total_clients'] ?> clients
            <?php if ($stats['recent_clients'] > 0): ?>
                | <?= $stats['recent_clients'] ?> added in last 30 days
            <?php endif; ?>
        </p>
    </div>
    <a class="btn btn-primary" href="/php_crud/public/clients/create" role="button">
        <i class="bi bi-plus-circle"></i> Add New Client
    </a>
</div>

<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Address</th>
                        <th>Created At</th>
                        <?php if ($isAdmin): ?>
                        <th>Created By</th>
                        <?php endif; ?>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                    <tr>
                        <td colspan="<?= $isAdmin ? '8' : '7' ?>" class="text-center text-muted py-4">
                            <i class="bi bi-inbox"></i> No records found
                        </td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                        <tr>
                            <td><?= htmlspecialchars($client['id']) ?></td>
                            <td><?= htmlspecialchars($client['name']) ?></td>
                            <td><?= htmlspecialchars($client['email']) ?></td>
                            <td><?= htmlspecialchars($client['phone']) ?></td>
                            <td><?= htmlspecialchars($client['address']) ?></td>
                            <td><?= htmlspecialchars($client['created_at']) ?></td>
                            <?php if ($isAdmin): ?>
                            <td><?= htmlspecialchars($client['creator_name'] ?? 'Unknown') ?></td>
                            <?php endif; ?>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a class="btn btn-outline-primary" 
                                       href="/php_crud/public/clients/<?= $client['id'] ?>/edit" 
                                       title="Edit">
                                        Edit
                                    </a>
                                    <a class="btn btn-outline-danger" 
                                       href="/php_crud/public/clients/<?= $client['id'] ?>/delete" 
                                       onclick="return confirm('Are you sure you want to delete this record?')"
                                       title="Delete">
                                        Delete
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/main.php';
?>
