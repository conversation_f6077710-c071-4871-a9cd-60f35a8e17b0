<?php

// Test script to verify MVC structure
require_once 'vendor/autoload.php';

echo "Testing MVC Structure...\n\n";

// Test Database connection
try {
    $db = App\Core\Database::getInstance();
    echo "✓ Database connection: OK\n";
} catch (Exception $e) {
    echo "✗ Database connection: " . $e->getMessage() . "\n";
}

// Test User model
try {
    $userModel = new App\Models\User();
    echo "✓ User model: OK\n";
} catch (Exception $e) {
    echo "✗ User model: " . $e->getMessage() . "\n";
}

// Test Client model
try {
    $clientModel = new App\Models\Client();
    echo "✓ Client model: OK\n";
} catch (Exception $e) {
    echo "✗ Client model: " . $e->getMessage() . "\n";
}

// Test Auth service
try {
    $auth = App\Core\Auth::getInstance();
    echo "✓ Auth service: OK\n";
} catch (Exception $e) {
    echo "✗ Auth service: " . $e->getMessage() . "\n";
}

// Test Controllers
try {
    $authController = new App\Controllers\AuthController();
    echo "✓ AuthController: OK\n";
} catch (Exception $e) {
    echo "✗ AuthController: " . $e->getMessage() . "\n";
}

try {
    $clientController = new App\Controllers\ClientController();
    echo "✓ ClientController: OK\n";
} catch (Exception $e) {
    echo "✗ ClientController: " . $e->getMessage() . "\n";
}

try {
    $homeController = new App\Controllers\HomeController();
    echo "✓ HomeController: OK\n";
} catch (Exception $e) {
    echo "✗ HomeController: " . $e->getMessage() . "\n";
}

// Test Router
try {
    $router = new App\Core\Router();
    echo "✓ Router: OK\n";
} catch (Exception $e) {
    echo "✗ Router: " . $e->getMessage() . "\n";
}

echo "\nMVC Structure Test Complete!\n";
echo "\nTo access the MVC application:\n";
echo "1. Make sure your web server is running\n";
echo "2. Navigate to: http://localhost/php_crud/public/\n";
echo "3. You should see the login page\n";
